# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    kb_driver_name = fields.Char(
        string='Driver Name',
        help='Name of the driver responsible for this picking'
    )
    kb_phone_no = fields.Char(
        string='Phone No.',
        help='Phone number of the driver'
    )
    kb_car_no = fields.Char(
        string='Car No.',
        help='Car/Vehicle number for this picking'
    )

    # Reference fields
    kb_ref_1 = fields.Char(
        string='Reference 1',
        help='First reference field'
    )
    kb_ref_2 = fields.Char(
        string='Reference 2',
        help='Second reference field'
    )
    kb_ref_3 = fields.Char(
        string='Reference 3',
        help='Third reference field'
    )
    kb_ref_4 = fields.Char(
        string='Reference 4',
        help='Fourth reference field'
    )
