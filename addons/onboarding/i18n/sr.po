# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onboarding
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <dragan.v<PERSON><PERSON>@gmail.com>, 2022
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__progress_ids
msgid "All Onboarding Progress Records (across companies)."
msgstr "All Onboarding Progress Records (across companies)."

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "All related Onboarding Progress Step Records (across companies)"
msgstr "All related Onboarding Progress Step Records (across companies)"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
msgid "Background color"
msgstr "Background color"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__blue
msgid "Blue"
msgstr "Blue"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__button_text
msgid "Button text"
msgstr "Button text"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Closing action"
msgstr "Closing action"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_background_color
msgid "Color gradient added to the panel's background."
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__company_id
msgid "Company"
msgstr "Kompanija"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_onboarding_state
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_step_state
msgid "Completion State"
msgstr "Completion State"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__description
msgid "Description"
msgstr "Opis"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__done
msgid "Done"
msgstr "Završeno"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_icon
msgid "Font Awesome Icon when completed"
msgstr "Font Awesome Icon when completed"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__id
msgid "ID"
msgstr "ID"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__just_done
msgid "Just done"
msgstr "Upravo završeno"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step____last_update
msgid "Last Modified on"
msgstr "Poslednja izmena dana"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_uid
msgid "Last Updated by"
msgstr "Poslednje izmenio/la"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_date
msgid "Last Updated on"
msgstr "Poslednje ažuriranje dana"

#. module: onboarding
#: code:addons/onboarding/models/onboarding_step.py:0
#, python-format
msgid "Let's do it"
msgstr "Hajde da to uradimo"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__name
msgid "Name of the onboarding"
msgstr "Name of the onboarding"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Name of the onboarding model action to execute when closing the panel."
msgstr ""
"Name of the onboarding model action to execute when closing the panel."

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"
msgstr ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__none
msgid "None"
msgstr "Ništa"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__not_done
msgid "Not done"
msgstr "Nije završeno"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__onboarding_id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__onboarding_id
msgid "Onboarding"
msgstr "Uvođenje"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress"
msgstr "Onboarding Progress"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__progress_ids
msgid "Onboarding Progress Records"
msgstr "Onboarding Progress Records"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "Onboarding Progress Step Records"
msgstr "Onboarding Progress Step Records"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress_step
msgid "Onboarding Progress Step Tracker"
msgstr "Onboarding Progress Step Tracker"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Onboarding Progress Step for the current context (company)."
msgstr "Onboarding Progress Step for the current context (company)."

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress
msgid "Onboarding Progress Tracker"
msgstr "Onboarding Progress Tracker"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress for the current context (company)."
msgstr "Onboarding Progress for the current context (company)."

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding_step
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_id
msgid "Onboarding Step"
msgstr "Uvodni korak"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_state
msgid "Onboarding Step Progress"
msgstr "Onboarding Step Progress"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_step
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_tree
msgid "Onboarding Steps"
msgstr "Onboarding Steps"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_onboarding_route_name_uniq
msgid "Onboarding alias must be unique."
msgstr "Onboarding alias must be unique."

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_state
msgid "Onboarding progress"
msgstr "Onboarding progress"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__step_ids
msgid "Onboarding steps"
msgstr "Onboarding steps"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Onboardings"
msgstr "Onboardings"

#. module: onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding_step
msgid "Onboardings Steps"
msgstr "Onboardings Steps"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__route_name
msgid "One word name"
msgstr "One word name"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid "Opening action"
msgstr "Opening action"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__orange
msgid "Orange"
msgstr "Orange"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_background_color
msgid "Panel's Background color"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_background_image
msgid "Panel's background image"
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__progress_step_ids
msgid "Progress Steps Trackers"
msgstr "Progress Steps Trackers"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__progress_id
msgid "Related Onboarding Progress Tracker"
msgstr "Related Onboarding Progress Tracker"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_id
msgid "Related onboarding tracked"
msgstr "Related onboarding tracked"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__sequence
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__sequence
msgid "Sequence"
msgstr "Niz"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_per_company
msgid "Should be done per company?"
msgstr "Should be done per company?"

#. module: onboarding
#: code:addons/onboarding/models/onboarding_step.py:0
#, python-format
msgid "Step Completed! - Click to review"
msgstr "Korak je završen! - Kliknite za pregled"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Step Progress"
msgstr "Step Progress"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__button_text
msgid "Text on the panel's button to start this step"
msgstr "Text on the panel's button to start this step"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_text
msgid "Text to show when step is completed"
msgstr "Text to show when step is completed"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_progress_onboarding_company_uniq
msgid ""
"There cannot be multiple records of the same onboarding completion for the "
"same company."
msgstr ""

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_progress_step_progress_step_uniq
msgid ""
"There cannot be multiple records of the same onboarding step completion for "
"the same Progress record."
msgstr ""

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__title
msgid "Title"
msgstr "Naslov"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Toggle visibility"
msgstr "Toggle visibility"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__violet
msgid "Violet"
msgstr "Violet"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_onboarding_closed
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__is_onboarding_closed
msgid "Was panel closed?"
msgstr "Was panel closed?"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_panel
msgid "onboarding.onboarding.step"
msgstr "onboarding.onboarding.step"
