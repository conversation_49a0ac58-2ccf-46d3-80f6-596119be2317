<odoo>
        <record id="kb_ref_fields_po_inherit_view" model="ir.ui.view">
            <field name="name">Purchase Order Excel</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="kb_ref_1_po"/>
                    <field name="kb_ref_2_po"/>
                    <field name="kb_ref_3_po"/>
                    <field name="kb_ref_4_po"/>
                </xpath>
            </field>
        </record>


    <record id="view_tree_kb_Purchase_Order2" model="ir.ui.view">
        <field name="name">Purchase Order2</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date_approve']" position="after">
                <field name="kb_ref_1_po"/>
                <field name="kb_ref_2_po"/>
                <field name="kb_ref_3_po"/>
                <field name="kb_ref_4_po"/>
           </xpath>
        </field>
    </record>

    <record id="view_tree_kb_Purchase_Order_view" model="ir.ui.view">
        <field name="name">Purchase Order</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_view_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="kb_ref_1_po"/>
                <field name="kb_ref_2_po"/>
                <field name="kb_ref_3_po"/>
                <field name="kb_ref_4_po"/>
           </xpath>
        </field>
    </record>
    <record id="viewpurchase_order_view_tree_search" model="ir.ui.view">
        <field name="name">Purchase Order</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_view_search" />
        <field name="arch" type="xml">
             <xpath expr="//search" position="inside">
                 <field name="kb_ref_1_po"/>
                 <field name="kb_ref_2_po"/>
                 <field name="kb_ref_3_po"/>
                 <field name="kb_ref_4_po"/>
           </xpath>
        </field>
    </record>
</odoo>
