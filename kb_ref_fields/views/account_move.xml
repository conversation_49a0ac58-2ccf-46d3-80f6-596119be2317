<odoo>
    <data>
        <record id="kb_ref_fields_am_inherit_view" model="ir.ui.view">
            <field name="name">Account Move Excel</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="kb_ref_1_am" />
                    <field name="kb_ref_2_am" />
                    <field name="kb_ref_3_am" />
                    <field name="kb_ref_4_am" />
                    <field name="kb_customer_ref_am" />
                    <field name="kb_order_date" readonly="True" />
                    <field name="kb_purchase_date" readonly="True" attrs="{'invisible': [('move_type', 'not in', 'in_invoice')]}"/>
                </xpath>
                 <xpath expr="//group[@name='misc_group']" position="inside">
                    <field name="kb_revision_checks"  groups="kb_ref_fields.group_Revision_administration"/>
                </xpath>
            </field>
        </record>


        <record id="view_tree_kb_ref_fields_am_view" model="ir.ui.view">
        <field name="name">account move line tree</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
              <field name="kb_revision_check" groups="kb_ref_fields.group_Revision_administration"/>
              <field name="kb_category_id"  invisible="1"/>
           </xpath>
        </field>
        </record>

        <record id="view_tree_amount_residual_view" model="ir.ui.view">
        <field name="name">account move line tree for residual </field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_residual']" position="after">
              <field name="cumulated_residual"/>
           </xpath>
        </field>
        </record>

        <record id="view_tree_kb_revision_move_am_view" model="ir.ui.view">
        <field name="name">account move tree</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
              <field name="kb_revision_checks" groups="kb_ref_fields.group_Revision_administration"/>
           </xpath>
        </field>
        </record>

        <record id="kb_reviewed_button_in_move" model="ir.actions.server">
            <field name="name">Reviewed</field>
            <field name="type">ir.actions.server</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="state">code</field>
            <field name="code">records.button_reviewed()</field>
        </record>

      <record id="view_account_move_line_view_tree_search" model="ir.ui.view">
        <field name="name">account move search</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_account_move_line_filter" />
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="kb_category_id" />
                <field name="balance" />
           </xpath>
        </field>
      </record>

      <record id="kb_reviewed_button" model="ir.actions.server">
        <field name="name">Reviewed</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="account.model_account_move_line"/>
        <field name="binding_model_id" ref="account.model_account_move_line"/>
        <field name="state">code</field>
        <field name="code">records.button_reviewed()</field>
      </record>

    </data>

</odoo>