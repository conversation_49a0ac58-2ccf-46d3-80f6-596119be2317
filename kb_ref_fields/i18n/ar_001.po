# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* kb_ref_fields
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-07 09:17+0000\n"
"PO-Revision-Date: 2024-01-07 09:17+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: kb_ref_fields
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_bank_statement_line__kb_customer_ref_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_move__kb_customer_ref_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_payment__kb_customer_ref_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_sale_order__kb_customer_ref_so
msgid "Customer Reference"
msgstr "مرجع العميل"

#. module: kb_ref_fields
#: model:ir.model,name:kb_ref_fields.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: kb_ref_fields
#: model:ir.model,name:kb_ref_fields.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: kb_ref_fields
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_bank_statement_line__kb_ref_1_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_move__kb_ref_1_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_payment__kb_ref_1_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_purchase_order__kb_ref_1_po
#: model:ir.model.fields,field_description:kb_ref_fields.field_sale_order__kb_ref_1_so
msgid " Driver Name"
msgstr "اسم السائق"

#. module: kb_ref_fields
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_bank_statement_line__kb_ref_2_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_move__kb_ref_2_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_account_payment__kb_ref_2_am
#: model:ir.model.fields,field_description:kb_ref_fields.field_purchase_order__kb_ref_2_po
#: model:ir.model.fields,field_description:kb_ref_fields.field_sale_order__kb_ref_2_so
msgid " Car No."
msgstr " رقم السيارة"

#. module: kb_ref_fields
#: model:ir.model,name:kb_ref_fields.model_sale_order
msgid "Sales Order"
msgstr ""
