from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class SaleOrder(models.Model):
    """inherited account account"""
    _inherit = "sale.order"

    
    kb_ref_1_so = fields.Char(
        string='Driver',
    )
    kb_ref_2_so = fields.Char(
        string='Car No.',
    )
    kb_ref_3_so = fields.Char(
        string='Identity No.',
    )
    kb_ref_4_so = fields.Char(
        string='Phone No.',
    )
    kb_customer_ref_so = fields.Char(
        string='Customer Reference',
    )
    kb_vendor_ref_so = fields.Char(
        string='Vendor Reference',
    )

    # _columns = {
    #     'state': fields.selection((('under','under revision')),),
    # }
    # state=fields.Selection(selection_add=[{'under','under revision'},])
    # state = fields.Selection(selection_add=[('under','under revision')])
    #
    # def under_state(self):
    #     self.state='under'

    # def _prepare_invoice(self):
    #     res = super()._prepare_invoice()
    #     res.update({
    #                 'kb_ref_1_am': self.kb_ref_1_so,
    #                 'kb_ref_2_am': self.kb_ref_2_so,
    #                 'kb_customer_ref_am': self.kb_customer_ref_so,
    #                 'kb_order_date': self.date_order,
    #                 })
    #     return res