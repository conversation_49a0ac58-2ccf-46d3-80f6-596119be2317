from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class Account<PERSON>ove(models.Model):
    """inherited account account"""
    _inherit = "account.move"

    
    kb_ref_1_am = fields.Char(
        string=' Driver Name',
    )
    kb_ref_2_am = fields.Char(
        string=' Car No.',
    )
    kb_ref_3_am = fields.Char(
        string='Identity No.',
    )
    kb_ref_4_am = fields.Char(
        string='Phone No.',
    )
    kb_customer_ref_am = fields.Char(
        string='Customer Reference',
    )
    kb_vendor_ref_am = fields.Char(
        string='Vendor Reference',
    )

    kb_order_date = fields.Datetime(
        string='Order Date',
    )
    kb_purchase_date = fields.Datetime(
        string='Order Date',
    )

    kb_revision_checks = fields.Boolean(
        string='Reviewed', tracking=True,
    )

    def button_reviewed(self):
        for rec in self:
            if rec.kb_revision_checks:
                if rec.kb_revision_checks == True:
                    rec.kb_revision_checks = False
                else:
                    rec.kb_revision_checks = True
            else:
                raise ValidationError(_('Check if have Reviewed button'))


class kb_AccountMoveline(models.Model):
    _inherit = "account.move.line"

    kb_revision_check = fields.<PERSON>olean(
        string='Reviewed', related='move_id.kb_revision_checks' ,tracking=True,
    )
    kb_category_id = fields.Many2many(
        string='Tag', related='partner_id.category_id'
    )

    def button_reviewed(self):
        for rec in self:
            if rec.move_id.kb_revision_checks == True:
                rec.move_id.kb_revision_checks = False
            else:
                rec.move_id.kb_revision_checks = True

    cumulated_residual= fields.Monetary(
        string='Cumulated Balance for residual',
        compute='_compute_cumulated_residual',
        currency_field='company_currency_id',
        exportable=False,
        help="Cumulated Balance for residual depending on the domain and the order chosen in the view.")

    @api.depends_context('order_cumulated_residual', 'domain_cumulated_residual')
    def _compute_cumulated_residual(self):
        if not self.env.context.get('order_cumulated_residual'):
            # We do not come from search_read, so we are not in a list view, so it doesn't make any sense to compute the Cumulated Balance for residual
            self.cumulated_residual = 0
            return

        # get the where clause
        query = self._where_calc(list(self.env.context.get('domain_cumulated_residual') or []))
        order_string = ", ".join(
            self._generate_order_by_inner(self._table, self.env.context.get('order_cumulated_residual'), query,
                                          reverse_direction=True))
        from_clause, where_clause, where_clause_params = query.get_sql()
        sql = """
               SELECT account_move_line.id, SUM(account_move_line.amount_residual) OVER (
                   ORDER BY %(order_by)s
                   ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
               )
               FROM %(from)s
               WHERE %(where)s
           """ % {'from': from_clause, 'where': where_clause or 'TRUE', 'order_by': order_string}
        self.env.cr.execute(sql, where_clause_params)
        result = {r[0]: r[1] for r in self.env.cr.fetchall()}
        print (result)
        for record in self:
            record.cumulated_residual = result[record.id]

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        def to_tuple(t):
            return tuple(map(to_tuple, t)) if isinstance(t, (list, tuple)) else t

        # Make an explicit order because we will need to reverse it
        order = (order or self._order) + ', id'
        # Add the domain and order by in order to compute the Cumulated Balance for residual in _compute_cumulated_residual
        contextualized = self.with_context(
            domain_cumulated_residual=to_tuple(domain or []),
            order_cumulated_residual=order,
        )
        return super(kb_AccountMoveline, contextualized).search_read(domain, fields, offset, limit, order)
